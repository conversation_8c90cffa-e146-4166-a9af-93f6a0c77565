#!/usr/bin/env python3
"""
简化后端启动器
Simple Backend Launcher
"""

import os
import sys
import subprocess
from pathlib import Path

def check_basic_dependencies():
    """检查基础依赖"""
    required_packages = ["fastapi", "uvicorn", "pydantic", "loguru"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    return missing_packages

def install_basic_dependencies():
    """安装基础依赖"""
    basic_packages = [
        "fastapi>=0.100.0",
        "uvicorn>=0.20.0", 
        "pydantic>=2.0.0",
        "loguru>=0.7.0",
        "python-multipart>=0.0.6",
        "aiofiles>=23.0.0"
    ]
    
    print("📦 Installing basic dependencies...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install"
        ] + basic_packages, check=True)
        print("✅ Basic dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_directories():
    """设置目录结构"""
    directories = [
        "logs",
        "output/animations", 
        "temp/animation_data",
        "backend/config"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ {directory}")

def start_backend():
    """启动后端服务"""
    print("🚀 Starting Motion Agent Backend...")
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = str(Path.cwd())
    
    try:
        # 启动uvicorn服务器
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "backend.main:app",
            "--host", "0.0.0.0",
            "--port", "9000",
            "--reload",
            "--log-level", "info"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start backend: {e}")
        return False
    except KeyboardInterrupt:
        print("\n🛑 Backend stopped by user")
        return True

def main():
    """主函数"""
    print("🎬 Motion Agent Simple Backend Launcher")
    print("=" * 50)
    
    # 1. 设置目录
    print("📁 Setting up directories...")
    setup_directories()
    
    # 2. 检查依赖
    print("\n🔍 Checking dependencies...")
    missing = check_basic_dependencies()
    
    if missing:
        print(f"\n🚫 Missing packages: {', '.join(missing)}")
        print("💡 Attempting to install basic dependencies...")
        
        if not install_basic_dependencies():
            print("❌ Failed to install dependencies")
            print("Please run: pip install fastapi uvicorn pydantic loguru python-multipart aiofiles")
            return 1
        
        # 重新检查
        print("\n🔍 Re-checking dependencies...")
        missing = check_basic_dependencies()
        if missing:
            print(f"❌ Still missing: {', '.join(missing)}")
            return 1
    
    print("\n✅ All basic dependencies available")
    
    # 3. 启动服务
    print("\n🚀 Starting backend service...")
    print("📍 Server will be available at: http://localhost:9000")
    print("📖 API docs will be available at: http://localhost:9000/docs")
    print("🔄 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    return 0 if start_backend() else 1

if __name__ == "__main__":
    sys.exit(main())
