#!/usr/bin/env python3
"""
修复后功能测试脚本
Fixed Features Test Script
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def test_animation_generation():
    """测试动画生成功能"""
    print("🎬 Testing Animation Generation...")
    
    try:
        from backend.animation.models import AnimationRequest
        from backend.animation.professional_pipeline import ProfessionalAnimationPipeline
        
        # 创建测试请求
        request = AnimationRequest(
            text="关羽登场，威风凛凛地走向前方",
            character_id="guanyu",
            quality_target="game_ready",
            frame_rate=30,
            export_format="fbx"
        )
        
        # 初始化管道
        pipeline = ProfessionalAnimationPipeline()
        
        # 生成动画
        print("   📝 Processing animation request...")
        response = await pipeline.process_animation_request(request)
        
        if response.success:
            print(f"   ✅ Animation generated successfully!")
            print(f"   📁 FBX file: {response.fbx_file_path}")
            print(f"   ⏱️  Processing time: {response.processing_time:.2f}s")
            print(f"   📊 Quality score: {response.quality_report.get('overall_score', 'N/A')}")
            
            # 检查文件是否存在
            if response.fbx_file_path and os.path.exists(response.fbx_file_path):
                file_size = os.path.getsize(response.fbx_file_path)
                print(f"   📏 File size: {file_size} bytes")
                return True
            else:
                print("   ⚠️  FBX file not found")
                return False
        else:
            print(f"   ❌ Animation generation failed: {response.error_message}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


async def test_api_endpoints():
    """测试API端点"""
    print("\n🌐 Testing API Endpoints...")
    
    try:
        # 测试预设搜索
        from backend.animation.animation_presets import AnimationPresetsLibrary
        
        presets_library = AnimationPresetsLibrary()
        search_results = presets_library.search_presets(["walk", "关羽"])
        
        print(f"   🔍 Preset search results: {len(search_results)} found")
        for preset in search_results[:3]:
            print(f"      • {preset.name} ({preset.category.value})")
        
        # 测试过渡优化
        from backend.animation.transition_optimizer import TransitionOptimizer
        
        optimizer = TransitionOptimizer()
        recommendations = optimizer.get_transition_recommendations("walk", "attack")
        
        print(f"   🔄 Transition recommendations:")
        print(f"      • Compatibility: {recommendations['compatibility_score']:.2f}")
        print(f"      • Type: {recommendations['recommended_type']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


async def test_file_validation():
    """测试文件验证"""
    print("\n✅ Testing File Validation...")
    
    try:
        from backend.animation.fbx_validator import FBXValidator
        
        # 创建测试文件
        test_file = "output/animations/test_validation.fbx"
        os.makedirs("output/animations", exist_ok=True)
        
        with open(test_file, "wb") as f:
            f.write(b"Kaydara FBX Binary  \x00\x1a\x00")
            f.write(b"\x00" * 500)
        
        validator = FBXValidator()
        validation_result = validator.validate_fbx_file(test_file)
        
        print(f"   📋 Validation result:")
        print(f"      • Valid: {validation_result['is_valid']}")
        print(f"      • File size: {validation_result['file_size']} bytes")
        print(f"      • Recommendations: {len(validation_result.get('recommendations', []))}")
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


async def test_quality_checking():
    """测试质量检查"""
    print("\n📊 Testing Quality Checking...")
    
    try:
        from backend.animation.quality_checker import AnimationQualityChecker
        
        checker = AnimationQualityChecker()
        
        test_data = {
            "action_sequence": {
                "actions": [
                    {"type": "walk", "duration": 2.0},
                    {"type": "attack", "duration": 1.0},
                    {"type": "defend", "duration": 1.5}
                ],
                "frame_rate": 30
            }
        }
        
        quality_result = checker.check_animation_quality(test_data)
        
        print(f"   📈 Quality analysis:")
        print(f"      • Overall score: {quality_result['overall_score']:.2f}")
        print(f"      • Quality level: {quality_result['quality_level']}")
        print(f"      • Issues found: {len(quality_result.get('issues', []))}")
        print(f"      • Recommendations: {len(quality_result.get('recommendations', []))}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 Motion Agent Fixed Features Test")
    print("=" * 50)
    
    # 确保必要的目录存在
    os.makedirs("output/animations", exist_ok=True)
    os.makedirs("temp/animation_data", exist_ok=True)
    
    tests = [
        ("Animation Generation", test_animation_generation),
        ("API Endpoints", test_api_endpoints),
        ("File Validation", test_file_validation),
        ("Quality Checking", test_quality_checking),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = "PASS" if result else "FAIL"
        except Exception as e:
            print(f"   ❌ Unexpected error: {e}")
            results[test_name] = "ERROR"
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 Test Results Summary")
    print("=" * 50)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result == "PASS")
    
    for test_name, result in results.items():
        status_icon = "✅" if result == "PASS" else "❌"
        print(f"{status_icon} {test_name}: {result}")
    
    print(f"\n📊 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! The system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")
    
    # 保存结果
    with open("fixed_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"📄 Results saved to: fixed_test_results.json")


if __name__ == "__main__":
    asyncio.run(main())
